# 游戏常量定义
import pygame

# 游戏窗口设置
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
GAME_WIDTH = 300
GAME_HEIGHT = 600
BLOCK_SIZE = 30

# 游戏板设置
BOARD_WIDTH = GAME_WIDTH // BLOCK_SIZE  # 10
BOARD_HEIGHT = GAME_HEIGHT // BLOCK_SIZE  # 20

# 颜色定义 (RGB)
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
GRAY = (128, 128, 128)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
ORANGE = (255, 165, 0)
PURPLE = (128, 0, 128)
CYAN = (0, 255, 255)

# 方块颜色
PIECE_COLORS = {
    'I': CYAN,
    'O': YELLOW,
    'T': PURPLE,
    'S': GREEN,
    'Z': RED,
    'J': BLUE,
    'L': ORANGE
}

# 游戏速度设置
INITIAL_FALL_TIME = 500  # 毫秒
FAST_FALL_TIME = 50     # 快速下降时间

# 得分设置
SCORE_PER_LINE = {
    1: 100,
    2: 300,
    3: 500,
    4: 800
}

# 键盘控制
CONTROLS = {
    pygame.K_LEFT: 'left',
    pygame.K_RIGHT: 'right',
    pygame.K_DOWN: 'down',
    pygame.K_UP: 'rotate',
    pygame.K_SPACE: 'drop'
}
