# 测试pygame是否能正常工作
import pygame
import sys

def test_pygame():
    try:
        print("正在初始化pygame...")
        pygame.init()
        
        print("正在创建显示窗口...")
        screen = pygame.display.set_mode((400, 300))
        pygame.display.set_caption("Pygame测试")
        
        print("pygame初始化成功！")
        print("窗口已创建，按ESC键或关闭窗口退出")
        
        clock = pygame.time.Clock()
        running = True
        
        while running:
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
            
            # 填充背景色
            screen.fill((50, 50, 100))
            
            # 绘制一个简单的矩形
            pygame.draw.rect(screen, (255, 255, 255), (150, 100, 100, 100))
            
            pygame.display.flip()
            clock.tick(60)
        
        pygame.quit()
        print("pygame测试完成")
        
    except Exception as e:
        print(f"pygame测试失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    test_pygame()
