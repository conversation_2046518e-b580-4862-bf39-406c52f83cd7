# 俄罗斯方块形状定义
import random
from constants import PIECE_COLORS

class TetrisPiece:
    # 定义7种俄罗斯方块的形状
    SHAPES = {
        'I': [
            ['.....',
             '..#..',
             '..#..',
             '..#..',
             '..#..'],
            ['.....',
             '.....',
             '####.',
             '.....',
             '.....']
        ],
        'O': [
            ['.....',
             '.....',
             '.##..',
             '.##..',
             '.....']
        ],
        'T': [
            ['.....',
             '.....',
             '.#...',
             '###..',
             '.....'],
            ['.....',
             '.....',
             '.#...',
             '.##..',
             '.#...'],
            ['.....',
             '.....',
             '.....',
             '###..',
             '.#...'],
            ['.....',
             '.....',
             '.#...',
             '##...',
             '.#...']
        ],
        'S': [
            ['.....',
             '.....',
             '.##..',
             '##...',
             '.....'],
            ['.....',
             '.#...',
             '.##..',
             '..#..',
             '.....']
        ],
        'Z': [
            ['.....',
             '.....',
             '##...',
             '.##..',
             '.....'],
            ['.....',
             '..#..',
             '.##..',
             '.#...',
             '.....']
        ],
        'J': [
            ['.....',
             '.#...',
             '.#...',
             '##...',
             '.....'],
            ['.....',
             '.....',
             '#....',
             '###..',
             '.....'],
            ['.....',
             '.##..',
             '.#...',
             '.#...',
             '.....'],
            ['.....',
             '.....',
             '###..',
             '..#..',
             '.....']
        ],
        'L': [
            ['.....',
             '..#..',
             '..#..',
             '.##..',
             '.....'],
            ['.....',
             '.....',
             '###..',
             '#....',
             '.....'],
            ['.....',
             '##...',
             '.#...',
             '.#...',
             '.....'],
            ['.....',
             '.....',
             '..#..',
             '###..',
             '.....']
        ]
    }

    def __init__(self, x=0, y=0, shape=None):
        if shape is None:
            self.shape = random.choice(list(self.SHAPES.keys()))
        else:
            self.shape = shape
        
        self.x = x
        self.y = y
        self.rotation = 0
        self.color = PIECE_COLORS[self.shape]

    def get_rotated_shape(self, rotation=None):
        """获取指定旋转状态的形状"""
        if rotation is None:
            rotation = self.rotation
        
        shapes = self.SHAPES[self.shape]
        return shapes[rotation % len(shapes)]

    def get_cells(self, x=None, y=None, rotation=None):
        """获取方块占用的所有格子坐标"""
        if x is None:
            x = self.x
        if y is None:
            y = self.y
        
        shape = self.get_rotated_shape(rotation)
        cells = []
        
        for row_idx, row in enumerate(shape):
            for col_idx, cell in enumerate(row):
                if cell == '#':
                    cells.append((x + col_idx, y + row_idx))
        
        return cells

    def rotate(self):
        """旋转方块"""
        self.rotation = (self.rotation + 1) % len(self.SHAPES[self.shape])

    def move(self, dx, dy):
        """移动方块"""
        self.x += dx
        self.y += dy

    def copy(self):
        """创建方块的副本"""
        new_piece = TetrisPiece(self.x, self.y, self.shape)
        new_piece.rotation = self.rotation
        return new_piece
