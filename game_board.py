# 游戏板逻辑
from constants import BOARD_WIDTH, BOARD_HEIGHT, BLACK, SCORE_PER_LINE

class GameBoard:
    def __init__(self):
        # 创建游戏板，用颜色表示每个格子的状态
        self.board = [[BLACK for _ in range(BOARD_WIDTH)] for _ in range(BOARD_HEIGHT)]
        self.score = 0
        self.lines_cleared = 0
        self.level = 1

    def is_valid_position(self, piece, x=None, y=None, rotation=None):
        """检查方块在指定位置是否有效"""
        cells = piece.get_cells(x, y, rotation)
        
        for cell_x, cell_y in cells:
            # 检查边界
            if cell_x < 0 or cell_x >= BOARD_WIDTH or cell_y >= BOARD_HEIGHT:
                return False
            
            # 检查是否与已有方块重叠（只检查y >= 0的部分）
            if cell_y >= 0 and self.board[cell_y][cell_x] != BLACK:
                return False
        
        return True

    def place_piece(self, piece):
        """将方块放置到游戏板上"""
        cells = piece.get_cells()
        
        for cell_x, cell_y in cells:
            if 0 <= cell_y < BOARD_HEIGHT and 0 <= cell_x < BOARD_WIDTH:
                self.board[cell_y][cell_x] = piece.color

    def clear_lines(self):
        """清除满行并返回清除的行数"""
        lines_to_clear = []
        
        # 找出所有满行
        for y in range(BOARD_HEIGHT):
            if all(cell != BLACK for cell in self.board[y]):
                lines_to_clear.append(y)
        
        # 清除满行
        for y in lines_to_clear:
            del self.board[y]
            self.board.insert(0, [BLACK for _ in range(BOARD_WIDTH)])
        
        # 更新得分和等级
        lines_cleared = len(lines_to_clear)
        if lines_cleared > 0:
            self.score += SCORE_PER_LINE.get(lines_cleared, 0) * self.level
            self.lines_cleared += lines_cleared
            self.level = self.lines_cleared // 10 + 1
        
        return lines_cleared

    def is_game_over(self):
        """检查游戏是否结束"""
        # 检查顶部行是否有方块
        return any(cell != BLACK for cell in self.board[0])

    def get_board_state(self):
        """获取游戏板状态的副本"""
        return [row[:] for row in self.board]

    def reset(self):
        """重置游戏板"""
        self.board = [[BLACK for _ in range(BOARD_WIDTH)] for _ in range(BOARD_HEIGHT)]
        self.score = 0
        self.lines_cleared = 0
        self.level = 1
