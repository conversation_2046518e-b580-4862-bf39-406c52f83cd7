# 俄罗斯方块主游戏文件
import pygame
import sys
import random
from constants import *
from tetris_pieces import TetrisP<PERSON>ce
from game_board import GameBoard

class TetrisGame:
    def __init__(self):
        pygame.init()
        self.screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("俄罗斯方块")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.small_font = pygame.font.Font(None, 24)
        
        # 游戏状态
        self.board = GameBoard()
        self.current_piece = None
        self.next_piece = None
        self.fall_time = 0
        self.fall_speed = INITIAL_FALL_TIME
        self.game_over = False
        self.paused = False
        
        # 生成第一个方块
        self.spawn_new_piece()

    def spawn_new_piece(self):
        """生成新方块"""
        if self.next_piece is None:
            self.next_piece = TetrisPiece(BOARD_WIDTH // 2 - 2, -1)

        self.current_piece = self.next_piece
        self.next_piece = TetrisPiece(BOARD_WIDTH // 2 - 2, -1)

        # 检查新方块是否能放置（稍微向上一点开始）
        if not self.board.is_valid_position(self.current_piece):
            self.game_over = True

    def handle_input(self):
        """处理用户输入"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                return False
            
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_ESCAPE:
                    return False
                
                if self.game_over:
                    if event.key == pygame.K_r:
                        self.restart_game()
                    continue
                
                if event.key == pygame.K_p:
                    self.paused = not self.paused
                    continue
                
                if self.paused:
                    continue
                
                # 处理游戏控制
                if event.key in CONTROLS:
                    action = CONTROLS[event.key]
                    self.handle_action(action)
        
        return True

    def handle_action(self, action):
        """处理游戏动作"""
        if not self.current_piece:
            return
        
        if action == 'left':
            if self.board.is_valid_position(self.current_piece, 
                                          self.current_piece.x - 1, 
                                          self.current_piece.y):
                self.current_piece.move(-1, 0)
        
        elif action == 'right':
            if self.board.is_valid_position(self.current_piece, 
                                          self.current_piece.x + 1, 
                                          self.current_piece.y):
                self.current_piece.move(1, 0)
        
        elif action == 'down':
            if self.board.is_valid_position(self.current_piece, 
                                          self.current_piece.x, 
                                          self.current_piece.y + 1):
                self.current_piece.move(0, 1)
        
        elif action == 'rotate':
            test_piece = self.current_piece.copy()
            test_piece.rotate()
            if self.board.is_valid_position(test_piece):
                self.current_piece.rotate()
        
        elif action == 'drop':
            while self.board.is_valid_position(self.current_piece, 
                                             self.current_piece.x, 
                                             self.current_piece.y + 1):
                self.current_piece.move(0, 1)

    def update(self, dt):
        """更新游戏状态"""
        if self.game_over or self.paused:
            return

        # 方块自动下落
        self.fall_time += dt
        current_fall_speed = max(FAST_FALL_TIME, self.fall_speed - (self.board.level - 1) * 50)

        if self.fall_time >= current_fall_speed:
            self.fall_time = 0

            if self.current_piece and self.board.is_valid_position(self.current_piece,
                                                                 self.current_piece.x,
                                                                 self.current_piece.y + 1):
                self.current_piece.move(0, 1)
            else:
                # 方块着陆
                if self.current_piece:
                    self.board.place_piece(self.current_piece)
                    lines_cleared = self.board.clear_lines()

                    # 检查游戏是否结束
                    if self.board.is_game_over():
                        self.game_over = True
                        return

                    self.spawn_new_piece()

    def draw_board(self):
        """绘制游戏板"""
        board_x = 50
        board_y = 50
        
        # 绘制游戏板背景
        pygame.draw.rect(self.screen, WHITE, 
                        (board_x - 2, board_y - 2, 
                         GAME_WIDTH + 4, GAME_HEIGHT + 4))
        pygame.draw.rect(self.screen, BLACK, 
                        (board_x, board_y, GAME_WIDTH, GAME_HEIGHT))
        
        # 绘制已放置的方块
        board_state = self.board.get_board_state()
        for y in range(BOARD_HEIGHT):
            for x in range(BOARD_WIDTH):
                if board_state[y][x] != BLACK:
                    rect = pygame.Rect(board_x + x * BLOCK_SIZE, 
                                     board_y + y * BLOCK_SIZE, 
                                     BLOCK_SIZE, BLOCK_SIZE)
                    pygame.draw.rect(self.screen, board_state[y][x], rect)
                    pygame.draw.rect(self.screen, WHITE, rect, 1)
        
        # 绘制当前方块
        if self.current_piece:
            cells = self.current_piece.get_cells()
            for cell_x, cell_y in cells:
                if 0 <= cell_x < BOARD_WIDTH and cell_y >= 0:
                    rect = pygame.Rect(board_x + cell_x * BLOCK_SIZE, 
                                     board_y + cell_y * BLOCK_SIZE, 
                                     BLOCK_SIZE, BLOCK_SIZE)
                    pygame.draw.rect(self.screen, self.current_piece.color, rect)
                    pygame.draw.rect(self.screen, WHITE, rect, 1)

    def draw_ui(self):
        """绘制用户界面"""
        ui_x = 400
        ui_y = 50
        
        # 绘制得分
        score_text = self.font.render(f"得分: {self.board.score}", True, WHITE)
        self.screen.blit(score_text, (ui_x, ui_y))
        
        # 绘制等级
        level_text = self.font.render(f"等级: {self.board.level}", True, WHITE)
        self.screen.blit(level_text, (ui_x, ui_y + 40))
        
        # 绘制消除行数
        lines_text = self.font.render(f"行数: {self.board.lines_cleared}", True, WHITE)
        self.screen.blit(lines_text, (ui_x, ui_y + 80))
        
        # 绘制下一个方块
        next_text = self.font.render("下一个:", True, WHITE)
        self.screen.blit(next_text, (ui_x, ui_y + 140))

        # 绘制下一个方块的背景框
        next_bg = pygame.Rect(ui_x, ui_y + 170, 100, 100)
        pygame.draw.rect(self.screen, GRAY, next_bg, 1)

        if self.next_piece:
            shape = self.next_piece.get_rotated_shape(0)
            # 计算方块的中心位置
            start_x = ui_x + 10
            start_y = ui_y + 180

            for row_idx, row in enumerate(shape):
                for col_idx, cell in enumerate(row):
                    if cell == '#':
                        rect = pygame.Rect(start_x + col_idx * 18,
                                         start_y + row_idx * 18,
                                         16, 16)
                        pygame.draw.rect(self.screen, self.next_piece.color, rect)
                        pygame.draw.rect(self.screen, WHITE, rect, 1)
        
        # 绘制控制说明
        controls_y = ui_y + 300
        controls = [
            "控制说明:",
            "← → : 左右移动",
            "↓ : 快速下降", 
            "↑ : 旋转",
            "空格 : 直接落下",
            "P : 暂停",
            "ESC : 退出"
        ]
        
        for i, text in enumerate(controls):
            color = WHITE if i == 0 else GRAY
            font = self.font if i == 0 else self.small_font
            control_text = font.render(text, True, color)
            self.screen.blit(control_text, (ui_x, controls_y + i * 25))

    def draw_game_over(self):
        """绘制游戏结束界面"""
        overlay = pygame.Surface((WINDOW_WIDTH, WINDOW_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))

        game_over_text = self.font.render("游戏结束!", True, WHITE)
        score_text = self.font.render(f"最终得分: {self.board.score}", True, WHITE)
        level_text = self.small_font.render(f"达到等级: {self.board.level}", True, WHITE)
        lines_text = self.small_font.render(f"消除行数: {self.board.lines_cleared}", True, WHITE)
        restart_text = self.small_font.render("按 R 重新开始", True, WHITE)
        exit_text = self.small_font.render("按 ESC 退出", True, WHITE)

        text_rect = game_over_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 - 80))
        score_rect = score_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 - 40))
        level_rect = level_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 - 10))
        lines_rect = lines_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 + 20))
        restart_rect = restart_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 + 60))
        exit_rect = exit_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 + 90))

        self.screen.blit(game_over_text, text_rect)
        self.screen.blit(score_text, score_rect)
        self.screen.blit(level_text, level_rect)
        self.screen.blit(lines_text, lines_rect)
        self.screen.blit(restart_text, restart_rect)
        self.screen.blit(exit_text, exit_rect)

    def draw_pause(self):
        """绘制暂停界面"""
        overlay = pygame.Surface((WINDOW_WIDTH, WINDOW_HEIGHT))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 0))
        
        pause_text = self.font.render("游戏暂停", True, WHITE)
        continue_text = self.small_font.render("按 P 继续", True, WHITE)
        
        text_rect = pause_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 - 25))
        continue_rect = continue_text.get_rect(center=(WINDOW_WIDTH // 2, WINDOW_HEIGHT // 2 + 25))
        
        self.screen.blit(pause_text, text_rect)
        self.screen.blit(continue_text, continue_rect)

    def restart_game(self):
        """重新开始游戏"""
        self.board.reset()
        self.current_piece = None
        self.next_piece = None
        self.fall_time = 0
        self.game_over = False
        self.paused = False
        self.spawn_new_piece()

    def run(self):
        """主游戏循环"""
        running = True
        
        while running:
            dt = self.clock.tick(60)  # 60 FPS
            
            # 处理输入
            running = self.handle_input()
            
            # 更新游戏状态
            self.update(dt)
            
            # 绘制
            self.screen.fill(BLACK)
            self.draw_board()
            self.draw_ui()
            
            if self.game_over:
                self.draw_game_over()
            elif self.paused:
                self.draw_pause()
            
            pygame.display.flip()
        
        pygame.quit()
        sys.exit()

if __name__ == "__main__":
    game = TetrisGame()
    game.run()
