# 简化版俄罗斯方块测试
import pygame
import sys
import random

# 初始化pygame
pygame.init()

# 常量
WINDOW_WIDTH = 800
WINDOW_HEIGHT = 600
BLOCK_SIZE = 30
BOARD_WIDTH = 10
BOARD_HEIGHT = 20

# 颜色
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)

def main():
    try:
        print("正在启动俄罗斯方块游戏...")
        
        # 创建窗口
        screen = pygame.display.set_mode((WINDOW_WIDTH, WINDOW_HEIGHT))
        pygame.display.set_caption("俄罗斯方块 - 简化版")
        clock = pygame.time.Clock()
        font = pygame.font.Font(None, 36)
        
        print("游戏窗口已创建")
        print("控制说明:")
        print("- ESC: 退出游戏")
        print("- 空格: 改变颜色")
        
        # 游戏变量
        running = True
        current_color = RED
        colors = [RED, GREEN, BLUE, <PERSON><PERSON><PERSON><PERSON>]
        color_index = 0
        
        while running:
            # 处理事件
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    running = False
                elif event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_ESCAPE:
                        running = False
                    elif event.key == pygame.K_SPACE:
                        color_index = (color_index + 1) % len(colors)
                        current_color = colors[color_index]
            
            # 清屏
            screen.fill(BLACK)
            
            # 绘制游戏区域边框
            game_area = pygame.Rect(50, 50, BOARD_WIDTH * BLOCK_SIZE, BOARD_HEIGHT * BLOCK_SIZE)
            pygame.draw.rect(screen, WHITE, game_area, 2)
            
            # 绘制一些示例方块
            for i in range(5):
                for j in range(3):
                    x = 50 + (i + 2) * BLOCK_SIZE
                    y = 50 + (j + 15) * BLOCK_SIZE
                    block_rect = pygame.Rect(x, y, BLOCK_SIZE, BLOCK_SIZE)
                    pygame.draw.rect(screen, current_color, block_rect)
                    pygame.draw.rect(screen, WHITE, block_rect, 1)
            
            # 绘制文本
            title_text = font.render("俄罗斯方块 - 测试版", True, WHITE)
            screen.blit(title_text, (400, 100))
            
            instruction_text = font.render("按空格改变颜色", True, WHITE)
            screen.blit(instruction_text, (400, 150))
            
            exit_text = font.render("按ESC退出", True, WHITE)
            screen.blit(exit_text, (400, 200))
            
            # 更新显示
            pygame.display.flip()
            clock.tick(60)
        
        print("游戏正常退出")
        
    except Exception as e:
        print(f"游戏运行出错: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        pygame.quit()
        print("pygame已关闭")

if __name__ == "__main__":
    main()
